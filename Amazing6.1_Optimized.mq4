//+------------------------------------------------------------------+
//|                                           Amazing6.1_Optimized.mq4 |
//|                        Copyright © 2024, Optimized Grid Trading EA |
//|                                    Enhanced with Advanced Features |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2024, Optimized Grid Trading EA"
#property link      "https://github.com/optimized-ea"
#property description "---------------------------------------------"
#property description "优化版网格交易EA - 动态网格 + 智能风控"
#property description "Features: Dynamic Grid, Multi-layer Risk Control"
#property description "Market State Recognition, Adaptive Closing"
#property description "Performance Monitoring System"
#property description "---------------------------------------------"

#property version    "6.1.1"
#property strict

// 枚举定义
enum opentime {
    OPEN_ALWAYS = 0,     // 总是开单
    OPEN_FIRST_BAR = 1,  // 新K线开单
    OPEN_TICK = 2,       // 每tick开单
    OPEN_CUSTOM = 3      // 自定义开单
};

enum MarketState {
    MARKET_TRENDING_UP = 0,    // 上升趋势
    MARKET_TRENDING_DOWN = 1,  // 下降趋势
    MARKET_RANGING = 2,        // 震荡市场
    MARKET_HIGH_VOLATILITY = 3 // 高波动性
};

//+------------------------------------------------------------------+
//| 基础交易参数                                                        |
//+------------------------------------------------------------------+
extern double lot = 0.01;                    // 起始手数
extern int Magic = 668;                      // 魔术数字
extern int Step = 100;                       // 基础网格间距
extern int FirstStep = 30;                   // 首单距离
extern int TwoStep = 100;                    // 第二补单间距
extern int Totals = 50;                      // 最大单量
extern double K_Lot = 1.3;                   // 倍率
extern double PlusLot = 0;                   // 累加手数
extern double Maxlot = 10;                   // 最大开单手数
extern int DigitsLot = 2;                    // 下单量的小数位

//+------------------------------------------------------------------+
//| 风险管理参数                                                        |
//+------------------------------------------------------------------+
extern double MaxLoss = 100000;              // 单边浮亏超过多少不继续加仓
extern double MaxLossCloseAll = 50;          // 单边平仓限制
extern double CloseAll = 0.5;                // 整体平仓金额
extern double StopProfit = 2;                // 单边平仓金额
extern double StopLoss = 0;                  // 止损金额
extern bool Profit = true;                   // 单边平仓金额累加开关
extern int MaxSpread = 32;                   // 点差限制
extern int Leverage = 100;                   // 平台杠杆限制

//+------------------------------------------------------------------+
//| 动态网格优化参数                                                    |
//+------------------------------------------------------------------+
extern bool EnableDynamicGrid = true;        // 启用动态网格
extern int ATR_Period = 14;                  // ATR周期
extern double ATR_Multiplier = 1.5;          // ATR倍数
extern double MinGridRatio = 0.5;            // 最小网格比例
extern double MaxGridRatio = 3.0;            // 最大网格比例

//+------------------------------------------------------------------+
//| 多层风险控制参数                                                    |
//+------------------------------------------------------------------+
extern bool EnableAdvancedRisk = true;       // 启用高级风险控制
extern double MaxCorrelationRisk = 0.8;      // 最大相关性风险
extern bool AvoidNewsTime = true;            // 避开新闻时间
extern double MinTrendStrength = 25.0;       // 最小趋势强度(ADX)
extern double MaxDrawdownPercent = 20.0;     // 最大回撤百分比

//+------------------------------------------------------------------+
//| 市场状态识别参数                                                    |
//+------------------------------------------------------------------+
extern bool EnableMarketAnalysis = true;     // 启用市场分析
extern int MA_Period = 50;                   // 移动平均线周期
extern int ADX_Period = 14;                  // ADX周期
extern double VolatilityThreshold = 2.0;     // 波动性阈值

//+------------------------------------------------------------------+
//| 自适应平仓参数                                                      |
//+------------------------------------------------------------------+
extern bool EnableAdaptiveClose = true;      // 启用自适应平仓
extern bool EnablePartialClose = true;       // 启用部分平仓
extern double PartialCloseRatio = 0.7;       // 部分平仓比例
extern double PartialClosePercent = 50.0;    // 部分平仓百分比
extern bool EnableMultiTimeframe = true;     // 启用多时间框架

//+------------------------------------------------------------------+
//| 性能监控参数                                                        |
//+------------------------------------------------------------------+
extern bool EnablePerformanceMonitor = true; // 启用性能监控
extern int MonitorPeriod = 100;              // 监控周期(交易数)
extern double MinSharpeRatio = 1.0;          // 最小夏普比率
extern double AutoAdjustThreshold = 0.8;     // 自动调整阈值

//+------------------------------------------------------------------+
//| 时间控制参数                                                        |
//+------------------------------------------------------------------+
extern string EA_StartTime = "00:00";        // EA开始时间
extern string EA_StopTime = "24:00";         // EA结束时间
extern string Limit_StartTime = "00:00";     // 限价开始时间
extern string Limit_StopTime = "24:00";      // 限价结束时间
extern int sleep = 30;                       // 开单时间间距(秒)
extern int NextTime = 0;                     // 整体平仓后多少秒后新局

//+------------------------------------------------------------------+
//| 价格限制参数                                                        |
//+------------------------------------------------------------------+
extern double On_top_of_this_price_not_Buy_first_order = 0;    // B以上不开(首)
extern double On_under_of_this_price_not_Sell_first_order = 0; // S以下不开(首)
extern double On_top_of_this_price_not_Buy_order = 0;          // B以上不开(补)
extern double On_under_of_this_price_not_Sell_order = 0;       // S以下不开(补)

//+------------------------------------------------------------------+
//| 其他参数                                                            |
//+------------------------------------------------------------------+
extern opentime OpenMode = 3;                // 开单模式
extern ENUM_TIMEFRAMES TimeZone = 1;         // 开单时区
extern double Money = 0;                     // 浮亏多少启用第二参数
extern bool CloseBuySell = true;             // 逆势保护开关
extern bool HomeopathyCloseAll = true;       // 顺势保护开关
extern bool Homeopathy = false;              // 完全对锁时挂上顺势开关
extern bool Over = false;                    // 平仓后停止交易

//+------------------------------------------------------------------+
//| 全局变量                                                            |
//+------------------------------------------------------------------+
datetime LastTradeTime = 0;                  // 上次交易时间
datetime LastBarTime = 0;                    // 上次K线时间
bool GlobalTradeAllowed = true;               // 全局交易允许标志
MarketState CurrentMarketState = MARKET_RANGING; // 当前市场状态

// 性能监控变量
double TotalProfit = 0;                       // 总盈利
double MaxDrawdown = 0;                       // 最大回撤
double PeakEquity = 0;                        // 峰值净值
int TotalTrades = 0;                          // 总交易数
int WinningTrades = 0;                        // 盈利交易数
double SharpeRatio = 0;                       // 夏普比率

// 动态网格变量
double DynamicStep = 0;                       // 动态网格间距
double CurrentATR = 0;                        // 当前ATR值

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("Amazing6.1 Optimized EA 初始化开始...");
    
    // 初始化时间字符串
    InitializeTimeStrings();
    
    // 初始化性能监控
    if (EnablePerformanceMonitor) {
        InitializePerformanceMonitor();
    }
    
    // 初始化动态网格
    if (EnableDynamicGrid) {
        InitializeDynamicGrid();
    }
    
    // 验证参数
    if (!ValidateParameters()) {
        Print("参数验证失败，EA停止运行");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    Print("Amazing6.1 Optimized EA 初始化完成");
    PlaySound("Starting.wav");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // 清理图表对象
    ObjectsDeleteAll(0, -1);
    
    // 保存性能数据
    if (EnablePerformanceMonitor) {
        SavePerformanceData();
    }
    
    Print("Amazing6.1 Optimized EA 已停止运行");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // 检查交易时间
    if (!IsTradeTimeAllowed()) {
        return;
    }
    
    // 更新市场状态
    if (EnableMarketAnalysis) {
        UpdateMarketState();
    }
    
    // 更新动态网格
    if (EnableDynamicGrid) {
        UpdateDynamicGrid();
    }
    
    // 风险检查
    if (EnableAdvancedRisk && !PassRiskCheck()) {
        return;
    }
    
    // 主要交易逻辑
    ExecuteMainTradingLogic();
    
    // 自适应平仓检查
    if (EnableAdaptiveClose) {
        CheckAdaptiveClose();
    }
    
    // 性能监控
    if (EnablePerformanceMonitor) {
        UpdatePerformanceMetrics();
    }
    
    // 更新显示信息
    UpdateDisplayInfo();
}

//+------------------------------------------------------------------+
//| 初始化函数声明                                                      |
//+------------------------------------------------------------------+
void InitializeTimeStrings();
void InitializePerformanceMonitor();
void InitializeDynamicGrid();
bool ValidateParameters();

//+------------------------------------------------------------------+
//| 核心功能函数声明                                                    |
//+------------------------------------------------------------------+
bool IsTradeTimeAllowed();
void UpdateMarketState();
void UpdateDynamicGrid();
bool PassRiskCheck();
void ExecuteMainTradingLogic();
void CheckAdaptiveClose();
void UpdatePerformanceMetrics();
void UpdateDisplayInfo();
void SavePerformanceData();

// 继续在下一部分实现这些函数...
